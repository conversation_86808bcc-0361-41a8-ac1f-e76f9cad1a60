import { getTranslation } from '@/lib/i18n';
import FAQ from '@/app/components/ui/FAQ';
import Hero from '@/app/components/ui/Hero';
import KeyFeatures from '@/app/components/ui/KeyFeatures';
import UserFeedback from '@/app/components/ui/UserFeedback';
import FeaturedOn from '@/app/components/ui/FeaturedOn';

export default async function Home({ params: { locale } }) {
  const t = function (key) {
    return getTranslation(locale, key);
  }

  return (
    <>
      <div className="page-container">
        <div className="section">
          <Hero locale={locale} />
        </div>
        <div className="section bg-gray-100 dark:bg-gray-800 rounded mt-10">
          <h2 className="text-2xl font-bold px-2 py-4">{t('What is Unix Timestamp Converter')}</h2>
          <div className="px-2">
            <p>
              {t('A Unix timestamp converter is a vital tool for managing time data in programming and data analysis. A Unix timestamp is the number of seconds since January 1, 1970, 00:00:00 UTC, known as the Unix Epoch. This compact numerical format is widely used in databases, APIs, and systems for its simplicity and compatibility.')}
            </p>
            <p>{t('With our Unix timestamp converter, you can easily perform timestamp to date conversions (e.g., 1697059200 to "October 12, 2023, 00:00:00 UTC") and date to timestamp conversions (e.g., "October 12, 2023" to 1697059200). These features are perfect for developers working on user interfaces, debugging logs, or integrating APIs with different time formats.')}</p>
            <p>{t('The 2038 problem affects older 32-bit systems, where timestamps may overflow after January 19, 2038. Modern 64-bit systems and our Unix timestamp converter handle this seamlessly.')}</p>
            <p>{t("Whether you're managing time zones or formatting dates, our Unix timestamp converter offers a fast, reliable solution for all your timestamp to date and date to timestamp needs.")}</p>
          </div>
        </div>
        <div className="section bg-gray-100 dark:bg-gray-800 rounded mt-20">
          <h2 className="text-2xl font-bold px-2 py-4">{t('How to Use Brat-Gen: Your Free Brat Generator Guide')}</h2>
          <div className="px-2">
            <p className='mb-4'>
              {t('Get started with Brat-Gen, your free Brat Generator, to create custom Brat-style covers in minutes! Follow these simple steps to unleash your Brat Summer vibe:')}
            </p>
            <ol className="list-decimal list-inside">
              <li className="mb-4">
                <span className="font-bold">{t('Enter Your Text:')}</span> {t('Type up to 50 characters in the text box to create your Brat-style message, instantly previewed on the canvas. Use bold fonts inspired by Charli XCX’s Brat album for that rebellious look.')}
              </li>
            </ol>
          </div>
        </div>
        <div className="section mt-10">
          <h3 className="text-2xl font-bold px-2 py-4">{t('Key Features of Brat-Gen’s Free Brat Generator')}</h3>
          <KeyFeatures locale={locale} />
        </div>
        <div className="section mt-10">
          <h3 className="text-2xl font-bold px-2 py-4">{t('Client Praise for Brat-Gen')}</h3>
          <UserFeedback locale={locale} />
        </div>
        <div className="section">
          <h3 className="text-2xl font-bold px-2 py-4">{t('Frequently Asked Questions')}</h3>
          <FAQ locale={locale} />
        </div>
        <div className="section">
          <FeaturedOn />
        </div>
      </div>
    </>
  );
}
