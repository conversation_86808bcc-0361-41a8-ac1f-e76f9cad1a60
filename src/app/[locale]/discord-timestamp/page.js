import DiscordTimestamp from '@/app/components/ui/DiscordTimestamp';
import { getTranslation } from '@/lib/i18n';

export default function DiscordTimestampPage({ params: { locale } }) {
  const t = function (key) {
    return getTranslation(locale, key);
  }

  return (
    <div className="page-container">
      <div className="section text-center pt-10 pb-2">
        <h1 className="text-5xl font-bold text-primary mb-2">{t("Discord Timestamp Converter")}</h1>
      </div>
      <div className='section mt-2 md:px-40'>
        <DiscordTimestamp locale={locale} />
      </div>
    </div>
  );
}
